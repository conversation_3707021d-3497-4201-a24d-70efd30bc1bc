{"type": "module", "private": true, "scripts": {"build": "vite build", "dev": "vite", "lint": "eslint .", "lint:fix": "eslint --fix .", "generate-sw": "workbox generateSW workbox-config.cjs", "build:apk": "./scripts/build-apk-capacitor.sh", "build:debug-apk": "./scripts/build-debug-apk.sh", "build:clean-apk": "./scripts/build-clean-debug-apk.sh", "prepare:apk": "./scripts/prepare-apk.sh", "apk:generate": "npm run build && npx @pwabuilder/cli package --platform android --output ./storage/app/apk", "cap:build": "npm run build && npx cap copy android && npx cap open android", "cap:sync": "npx cap sync android"}, "dependencies": {"@ai-sdk/vue": "^1.2.12", "@capacitor/android": "^7.4.2", "@capacitor/browser": "^7.0.1", "@capacitor/cli": "^7.4.2", "@capacitor/core": "^7.4.2", "@capgo/capacitor-social-login": "^7.8.3", "@headlessui/react": "^2.2.6", "@heroicons/react": "^2.2.0", "@heroicons/vue": "^2.2.0", "@iconify/vue": "^4.3.0", "@inertiajs/vue3": "^2.0.17", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/postcss": "^4.1.11", "@tailwindcss/typography": "^0.5.16", "@tanstack/vue-table": "^8.21.3", "@unhead/addons": "^1.11.20", "@unhead/vue": "^1.11.20", "@unovis/ts": "^1.5.2", "@unovis/vue": "^1.5.2", "@vee-validate/zod": "^4.15.1", "@vitejs/plugin-vue": "^5.2.4", "@vueuse/core": "^12.8.2", "@vueuse/integrations": "^12.8.2", "axios": "^1.11.0", "caniuse-lite": "^1.0.30001727", "change-case": "^5.4.4", "chokidar": "^4.0.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "concurrently": "^9.2.0", "date-fns": "^4.1.0", "embla-carousel-vue": "^8.6.0", "lucide-vue-next": "^0.462.0", "postcss": "^8.5.6", "pusher-js": "^8.4.0", "radix-vue": "^1.9.17", "reka-ui": "^2.4.1", "tailwind-merge": "^2.6.0", "tailwindcss": "^4.1.11", "tailwindcss-animate": "^1.0.7", "tw-animate-css": "^1.3.6", "v-calendar": "^3.1.2", "vaul-vue": "^0.2.1", "vee-validate": "^4.15.1", "vite": "6.0.11", "vite-plugin-qrcode": "^0.2.4", "vue": "^3.5.18", "vue-sonner": "^1.3.2", "ziggy-js": "^2.5.3", "zod": "^3.25.76"}, "devDependencies": {"@antfu/eslint-config": "^3.16.0", "@pwabuilder/cli": "^0.0.17", "eslint": "^9.32.0", "eslint-plugin-format": "^0.1.3", "eslint-plugin-tailwindcss": "^3.18.2", "eslint-plugin-vue": "^9.33.0", "laravel-vite-plugin": "^1.3.0", "typescript": "^5.9.2", "workbox-cli": "^7.3.0"}}