<?php

require_once __DIR__ . '/vendor/autoload.php';

use App\Models\User;
use App\Models\Students;
use App\Models\Faculty;
use App\Actions\Fortify\CreateNewUser;

// Bootstrap Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

echo "Testing User model methods after fix...\n\n";

// Test 1: Create a student user and test the methods
echo "=== Test 1: Student User ===\n";
$studentUser = new User([
    'name' => 'Test Student',
    'email' => '<EMAIL>',
    'role' => 'student',
    'person_id' => '205931', // Integer student ID
    'person_type' => Students::class,
]);

echo "Student User - isStudent(): " . ($studentUser->isStudent() ? 'true' : 'false') . "\n";
echo "Student User - isFaculty(): " . ($studentUser->isFaculty() ? 'true' : 'false') . "\n";
echo "Student User - getIsStudentAttribute(): " . ($studentUser->getIsStudentAttribute() ? 'true' : 'false') . "\n";
echo "Student User - getIsFacultyAttribute(): " . ($studentUser->getIsFacultyAttribute() ? 'true' : 'false') . "\n";

// Test 2: Create a faculty user and test the methods
echo "\n=== Test 2: Faculty User ===\n";
$facultyUser = new User([
    'name' => 'Test Faculty',
    'email' => '<EMAIL>',
    'role' => 'faculty',
    'person_id' => 'uuid-faculty-id', // UUID faculty ID
    'person_type' => Faculty::class,
]);

echo "Faculty User - isStudent(): " . ($facultyUser->isStudent() ? 'true' : 'false') . "\n";
echo "Faculty User - isFaculty(): " . ($facultyUser->isFaculty() ? 'true' : 'false') . "\n";
echo "Faculty User - getIsStudentAttribute(): " . ($facultyUser->getIsStudentAttribute() ? 'true' : 'false') . "\n";
echo "Faculty User - getIsFacultyAttribute(): " . ($facultyUser->getIsFacultyAttribute() ? 'true' : 'false') . "\n";

// Test 3: Test CreateNewUser action with student data
echo "\n=== Test 3: CreateNewUser Action ===\n";
try {
    $createNewUser = new CreateNewUser();
    
    // Test with student data (this should not cause the UUID error anymore)
    $studentData = [
        'name' => 'Test Student Registration',
        'email' => '<EMAIL>',
        'phone' => '1234567890',
        'password' => 'password123',
        'password_confirmation' => 'password123',
        'role' => 'student',
        'id' => '205931', // This is the ID that was causing the error
    ];
    
    echo "Testing CreateNewUser with student ID 205931...\n";
    echo "This should not cause a UUID error anymore.\n";
    
    // We won't actually create the user since we don't have the database set up,
    // but we can test the getPersonData method
    $reflection = new ReflectionClass($createNewUser);
    $method = $reflection->getMethod('getPersonData');
    $method->setAccessible(true);
    
    $personData = $method->invoke($createNewUser, '205931');
    if ($personData) {
        echo "Person data found for ID 205931\n";
        echo "Type: " . $personData['type'] . "\n";
        echo "Person ID: " . $personData['person_id'] . "\n";
    } else {
        echo "No person data found for ID 205931 (expected if student doesn't exist in DB)\n";
    }
    
} catch (Exception $e) {
    echo "Error during CreateNewUser test: " . $e->getMessage() . "\n";
}

echo "\n=== Test Complete ===\n";
echo "If no UUID-related errors appeared above, the fix is working correctly.\n";
